"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useUser } from "../../../contexts/UserContext";
import { useUser as useClerkUser } from "@clerk/nextjs";
import type { UserInfo, CategoryType } from "../../mypageRelease/[user_ID]/types";

interface SimpleUserInitializerProps {
  pageUserId: string;
  onUserDataReady: (data: {
    user: UserInfo | null;
    categories: CategoryType[];
    isOwnPage: boolean;
    domain: string;
  }) => void;
  onError: (error: string) => void;
}

export const SimpleUserInitializer: React.FC<SimpleUserInitializerProps> = ({
  pageUserId,
  onUserDataReady,
  onError,
}) => {
  const { userId } = useUser();
  const { user: clerkUser, isLoaded: isClerkLoaded } = useClerkUser();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [lastDragUpdate, setLastDragUpdate] = useState<number>(0);
  const [isRefreshBlocked, setIsRefreshBlocked] = useState(false);
  const [initializedPageUserId, setInitializedPageUserId] = useState<string | null>(null);
  const [confirmedIsOwnPage, setConfirmedIsOwnPage] = useState<boolean | null>(null);
  const [lastValidClerkUserId, setLastValidClerkUserId] = useState<string | null>(null);

  // カテゴリデータを再取得する関数
  const refreshCategories = useCallback(async () => {
    if (!pageUserId) return;

    // ドラッグ操作直後の場合は更新をスキップ
    const now = Date.now();
    if (isRefreshBlocked || (lastDragUpdate > 0 && now - lastDragUpdate < 3000)) {
      return;
    }

    try {
      const categoriesResponse = await fetch(`/api/getCategories?user_ID=${pageUserId}`);
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        const newCategories = Array.isArray(categoriesData) ? categoriesData : [];
        setCategories(newCategories);

        // 親コンポーネントに更新されたデータを通知
        if (user) {
          const isOwnPage = isClerkLoaded && clerkUser?.id === pageUserId;

          // ユーザーデータに既存の_lastSnsUpdateがある場合は保持
          const preservedUser = {
            ...user,
            ...(user._lastSnsUpdate && { _lastSnsUpdate: user._lastSnsUpdate })
          };

          onUserDataReady({
            user: preservedUser,
            categories: newCategories,
            isOwnPage,
            domain: (user as any).username || "",
          });
        }

        // sessionStorageのキャッシュも更新
        const cachedData = sessionStorage.getItem('cachedUserData');
        if (cachedData) {
          try {
            const parsedCache = JSON.parse(cachedData);
            parsedCache.categories = newCategories;
            parsedCache.timestamp = Date.now();
            sessionStorage.setItem('cachedUserData', JSON.stringify(parsedCache));
          } catch (cacheError) {
            // キャッシュ更新エラーは無視
          }
        }
      }
    } catch (error) {
      console.error('カテゴリデータの再取得に失敗しました:', error);
    }
  }, [pageUserId, clerkUser?.id, isClerkLoaded, isRefreshBlocked, lastDragUpdate]);

  // グローバルにrefreshCategories関数を公開
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).refreshCategories = refreshCategories;

      // ドラッグ操作の制御関数を公開
      (window as any).blockSimpleUserInitializerRefresh = () => {
        setIsRefreshBlocked(true);
        setLastDragUpdate(Date.now());
      };

      (window as any).unblockSimpleUserInitializerRefresh = () => {
        setTimeout(() => {
          setIsRefreshBlocked(false);
        }, 1000); // 1秒後に解除
      };
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).refreshCategories;
        delete (window as any).blockSimpleUserInitializerRefresh;
        delete (window as any).unblockSimpleUserInitializerRefresh;
      }
    };
  }, [refreshCategories]);

  // 安全な認証状態判定ロジック
  const getSafeIsOwnPage = useCallback(() => {
    // Clerkが読み込み完了していて、有効なユーザーIDがある場合
    if (isClerkLoaded && clerkUser?.id) {
      const currentIsOwnPage = clerkUser.id === pageUserId;

      // 有効なClerkユーザーIDを記録
      if (clerkUser.id !== lastValidClerkUserId) {
        setLastValidClerkUserId(clerkUser.id);
      }

      // 確定した認証状態を記録
      if (confirmedIsOwnPage !== currentIsOwnPage) {
        setConfirmedIsOwnPage(currentIsOwnPage);
      }

      return currentIsOwnPage;
    }

    // Clerkが未読み込みまたはユーザーIDが一時的にundefinedの場合
    // 既に確定した認証状態があればそれを使用
    if (confirmedIsOwnPage !== null && lastValidClerkUserId) {
      return confirmedIsOwnPage;
    }

    // 初回読み込み時のデフォルト
    return false;
  }, [isClerkLoaded, clerkUser?.id, pageUserId, confirmedIsOwnPage, lastValidClerkUserId]);

  // Clerkの読み込み完了後にisOwnPageを再評価（ドラッグ操作中は除く）
  useEffect(() => {
    if (user && categories.length >= 0 && !isRefreshBlocked) {
      const currentIsOwnPage = getSafeIsOwnPage();
      const currentDomain = (user as any).username || "";

      // 状態が実際に変更された場合のみ親コンポーネントに通知
      const shouldUpdate =
        !hasInitialized || // 初回のみ
        (hasInitialized && isClerkLoaded && clerkUser?.id && !(user as any)._isOwnPageSet); // Clerk読み込み完了後で未設定の場合

      if (shouldUpdate) {
        // ユーザーデータに既存の_lastSnsUpdateがある場合は保持
        const preservedUser = {
          ...user,
          ...(user._lastSnsUpdate && { _lastSnsUpdate: user._lastSnsUpdate }),
          _isOwnPageSet: true // フラグを設定して重複更新を防ぐ
        } as any;

        // 親コンポーネントに再通知
        onUserDataReady({
          user: preservedUser,
          categories,
          isOwnPage: currentIsOwnPage,
          domain: currentDomain,
        });
      }
    }
  }, [isClerkLoaded, clerkUser?.id, pageUserId, hasInitialized, isRefreshBlocked, user, categories, getSafeIsOwnPage]);

  useEffect(() => {
    // 既に初期化済みの場合、または同じページユーザーIDで初期化済みの場合は実行しない
    if (hasInitialized || initializedPageUserId === pageUserId) {
      return;
    }
    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // キャッシュされたデータを最初にチェック（超高速化）
        const cachedData = sessionStorage.getItem('cachedUserData');
        let userData = null;

        if (cachedData) {
          try {
            const parsedCache = JSON.parse(cachedData);
            // キャッシュが5分以内で、ユーザーIDが一致する場合
            if (Date.now() - parsedCache.timestamp < 300000 && parsedCache.user_ID === pageUserId) {
              // キャッシュからユーザーデータを構築
              userData = {
                userId: parsedCache.user_ID,
                user_ID: parsedCache.user_ID,
                name: parsedCache.name,
                profile_image: parsedCache.profile_image,
                background_image: parsedCache.background_image,
                username: parsedCache.username,
                user_name: parsedCache.name || '',
                self_introduction: parsedCache.self_introduction || '',
                contact_url: parsedCache.contact_url || '',
                contact_email: parsedCache.contact_email || '',
                email: parsedCache.email || ''
              };
            }
          } catch (cacheError) {
            // キャッシュエラーは無視して通常処理に進む
          }
        }

        // 変数を事前に宣言
        let transformedUserData = null;
        let profileData = {};
        let categoriesData = [];

        // キャッシュが無効な場合のみAPI呼び出し（並列化で高速化）
        if (!userData) {
          // 並列でデータを取得（パフォーマンス最適化）
          const [userResponse, profileResponse, categoriesResponse] = await Promise.allSettled([
            fetch(`/api/getUser?user_ID=${pageUserId}`),
            fetch(`/api/profile?user_ID=${pageUserId}`),
            fetch(`/api/getCategories?user_ID=${pageUserId}`)
          ]);

          // ユーザーデータの処理
          if (userResponse.status === 'fulfilled' && userResponse.value.ok) {
            userData = await userResponse.value.json();

            // 取得したデータをキャッシュに保存
            sessionStorage.setItem('cachedUserData', JSON.stringify({
              user_ID: userData.user_ID || userData.userId,
              username: userData.username,
              name: userData.name,
              profile_image: userData.profile_image,
              background_image: userData.background_image,
              self_introduction: userData.self_introduction,
              contact_url: userData.contact_url,
              contact_email: userData.contact_email,
              email: userData.email,
              timestamp: Date.now()
            }));
          } else {
            throw new Error(`ユーザー情報の取得に失敗しました`);
          }

          // プロフィール情報の処理
          if (profileResponse.status === 'fulfilled' && profileResponse.value.ok) {
            try {
              profileData = await profileResponse.value.json();
            } catch (error) {
              // プロフィール情報の処理失敗時も処理を続行
            }
          }

          // カテゴリ情報の処理
          if (categoriesResponse.status === 'fulfilled' && categoriesResponse.value.ok) {
            try {
              categoriesData = await categoriesResponse.value.json();
              setCategories(Array.isArray(categoriesData) ? categoriesData : []);
            } catch (error) {
              setCategories([]);
            }
          } else {
            setCategories([]);
          }
        } else {
          // キャッシュからデータを使用する場合でも、カテゴリ情報は取得
          try {
            const categoriesResponse = await fetch(`/api/getCategories?user_ID=${pageUserId}`);
            if (categoriesResponse.ok) {
              categoriesData = await categoriesResponse.json();
              setCategories(Array.isArray(categoriesData) ? categoriesData : []);
            } else {
              setCategories([]);
            }
          } catch (error) {
            setCategories([]);
          }
        }

        // ProfileDetailsコンポーネント用にデータを変換（共通処理）
        transformedUserData = {
          ...userData,
          selfIntroduction: userData.self_introduction || (profileData as any).selfIntroduction,
          website: userData.contact_url || (profileData as any).website,
          email: userData.email || (profileData as any).email,
          snsLinks: (profileData as any).snsLinks || {},
          snsOrder: (profileData as any).snsOrder || [],
          // 背景画像を確実に設定（複数のソースから取得）
          background_image: userData.background_image || (profileData as any).backgroundImage,
          backgroundImage: (profileData as any).backgroundImage || userData.background_image
        };

        // LCP要素（プロフィール画像）の即座プリロード
        if (transformedUserData.profile_image) {
          const preloadLink = document.createElement('link');
          preloadLink.rel = 'preload';
          preloadLink.as = 'image';
          preloadLink.href = transformedUserData.profile_image;
          preloadLink.fetchPriority = 'high';
          document.head.appendChild(preloadLink);
        }

        setUser(transformedUserData);

        // 安全な認証状態判定を使用
        const isOwnPage = getSafeIsOwnPage();

        // 親コンポーネントに通知
        const dataToSend = {
          user: transformedUserData,
          categories: Array.isArray(categoriesData) ? categoriesData : [],
          isOwnPage,
          domain: transformedUserData.username || "", // usernameを使用
        };

        console.log('🔍 [FLICKER DEBUG] SimpleUserInitializer sending data to parent:', {
          hasUser: !!dataToSend.user,
          categoriesCount: dataToSend.categories.length,
          isOwnPage: dataToSend.isOwnPage,
          domain: dataToSend.domain,
          timestamp: new Date().toISOString()
        });

        onUserDataReady(dataToSend);
        setHasInitialized(true); // 初期化完了フラグを設定
        setInitializedPageUserId(pageUserId); // 初期化したページユーザーIDを記録

      } catch (error) {
        onError(error instanceof Error ? error.message : 'データの取得に失敗しました');
        setHasInitialized(true); // エラーの場合も初期化完了とする
        setInitializedPageUserId(pageUserId); // エラーの場合も記録
      } finally {
        setIsLoading(false);
      }
    };

    if (pageUserId) {
      // 初期化開始ログを削除
      fetchUserData();
    } else {
      setHasInitialized(true);
      setInitializedPageUserId(null); // pageUserIdがない場合はnullを設定
    }
  }, [pageUserId, hasInitialized, initializedPageUserId]); // 依存配列を最小限に

  // ページユーザーIDが変更された場合は初期化状態をリセット
  useEffect(() => {
    if (pageUserId && initializedPageUserId && pageUserId !== initializedPageUserId) {
      setHasInitialized(false);
      setInitializedPageUserId(null);
      setConfirmedIsOwnPage(null);
      setLastValidClerkUserId(null);
    }
  }, [pageUserId, initializedPageUserId]);



  return null;
};
