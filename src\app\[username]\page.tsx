"use client";

import React, { useState, useEffect, useRef, useC<PERSON>back, useMemo, startTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { notFound } from "next/navigation";
import {
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  sortableKeyboardCoordinates,
  arrayMove,
} from "@dnd-kit/sortable";
import { Footer } from "@/components/Footer";
import CatLoadingAnimation from "@/components/CatLoadingAnimation/CatLoadingAnimation";

import { useUser as useUserContext } from "../../contexts/UserContext";
import { useUserData } from "../mypageRelease/[user_ID]/hooks/useUserData";
import { useUrlInitialization } from "../mypageRelease/[user_ID]/hooks/useUrlInitialization";
import type { UserInfo, CategoryType, RankingType } from "../mypageRelease/[user_ID]/types";

// 新しいページ用のコンポーネントをインポート
import { SimpleUserInitializer } from "./components/SimpleUserInitializer";
import { BrowserExtensionProtectionProvider } from "./components/BrowserExtensionProtectionProvider";
import { ProfileHeader } from "../mypageRelease/[user_ID]/components/ProfileHeader";
import { ProfileDetails } from "../mypageRelease/[user_ID]/components/ProfileDetails";
import { CategoryTabs } from "../mypageRelease/[user_ID]/components/CategoryTabs";
import { RankingList } from "../mypageRelease/[user_ID]/components/RankingList";
import { SideMenu } from "../mypageRelease/[user_ID]/components/SideMenu";
import { useRankingDragDrop } from "../mypageRelease/[user_ID]/hooks/useRankingDragDrop";
import { useSnsReordering } from "../mypageRelease/[user_ID]/hooks/useSnsReordering";
import { useCategoryManagement } from "../mypageRelease/[user_ID]/hooks/useCategoryManagement";

export default function UsernamePage({
  params,
}: {
  params: Promise<{ username: string }>;
}) {
  // ページ読み込み開始時間を記録（本番環境では削除予定）
  // const pageStartTime = performance.now();

  const router = useRouter();
  const searchParams = useSearchParams();
  const { user: clerkUser } = useUser();
  const { userId } = useUserContext();

  // 重複実行防止フラグ（強化版）
  const initializationRef = useRef(false);

  // URLパラメータからユーザー名を取得
  const [pageUsername, setPageUsername] = useState<string>("");
  const [pageUserId, setPageUserId] = useState<string>("");
  const [isParamsLoading, setIsParamsLoading] = useState(true);
  const [shouldShowNotFound, setShouldShowNotFound] = useState(false);

  // 超高速パラメータ処理とキャッシュ活用
  useEffect(() => {
    const ultraFastResolveParams = async () => {
      // 重複実行防止（refベース）
      if (initializationRef.current) {
        return;
      }
      initializationRef.current = true;

      try {


        const resolvedParams = await params;
        const username = resolvedParams.username || "";


        // 特定のパスを除外（静的ルートとして扱うべきもの）
        const excludedPaths = ['admin', 'api', 'guide', 'legal', 'privacy', 'terms', 'settings', 'sign-in', 'sign-up', 'sign-out', 'createAccount', 'forgot-password', 'change-email', 'change-password', 'withdrawal1', 'withdrawal2', 'test', 'test_api', 'setup'];

        if (excludedPaths.includes(username)) {
          setShouldShowNotFound(true);
          setIsParamsLoading(false);
          return;
        }

        setPageUsername(username);

        // 拡張されたキャッシュ戦略（パフォーマンス最適化）
        const cacheKey = `userData_${username}`;
        const cachedData = sessionStorage.getItem(cacheKey);
        if (cachedData) {
          try {
            const parsedCache = JSON.parse(cachedData);
            // キャッシュが10分以内で、ユーザー名が一致する場合（キャッシュ時間を延長）
            if (Date.now() - parsedCache.timestamp < 600000 && parsedCache.username === username) {
              setPageUserId(parsedCache.user_ID);
              setIsParamsLoading(false);

              // LCP要素（プロフィール画像）の即座プリロード
              if (parsedCache.profile_image) {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.as = 'image';
                preloadLink.href = parsedCache.profile_image;
                preloadLink.fetchPriority = 'high';
                document.head.appendChild(preloadLink);
              }

              // バックグラウンドでキャッシュを更新（stale-while-revalidate戦略）
              setTimeout(() => {
                fetch(`/api/getUserByUsername?username=${username}`)
                  .then(response => response.json())
                  .then(userData => {
                    if (userData.user_ID) {
                      sessionStorage.setItem(cacheKey, JSON.stringify({
                        ...userData,
                        username: username,
                        timestamp: Date.now()
                      }));
                    }
                  })
                  .catch(() => {
                    // バックグラウンド更新の失敗は無視
                  });
              }, 100);

              return;
            }
          } catch (cacheError) {
            // キャッシュエラーは無視して通常処理に進む
          }
        }

        // キャッシュが無効な場合のみAPI呼び出し（タイムアウト付き）
        if (username) {
          // 高速化: 3秒タイムアウトを設定
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 3000);

          try {
            const response = await fetch(`/api/getUserByUsername?username=${username}`, {
              signal: controller.signal,
              headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            });
            clearTimeout(timeoutId);

            if (response.ok) {
              const userData = await response.json();
              setPageUserId(userData.user_ID);

              // 取得したデータを拡張キャッシュに保存
              sessionStorage.setItem(cacheKey, JSON.stringify({
                user_ID: userData.user_ID,
                username: username,
                name: userData.name,
                profile_image: userData.profile_image,
                background_image: userData.background_image,
                self_introduction: userData.self_introduction,
                contact_url: userData.contact_url,
                contact_email: userData.contact_email,
                email: userData.email,
                timestamp: Date.now()
              }));
            } else {
              setShouldShowNotFound(true);
              setIsParamsLoading(false);
              return;
            }
          } catch (error) {
            clearTimeout(timeoutId);
            console.error('❌ ユーザー情報取得エラー:', error);

            // タイムアウトエラーの場合は特別な処理
            if (error.name === 'AbortError') {
              console.error('❌ API呼び出しタイムアウト - 3秒以内に応答がありませんでした');
            }

            setShouldShowNotFound(true);
            setIsParamsLoading(false);
            return;
          }
        }

        setIsParamsLoading(false);
      } catch (error) {
        console.error('❌ パラメータ解決エラー:', error);
        setShouldShowNotFound(true);
        setIsParamsLoading(false);
      }
    };

    ultraFastResolveParams();
  }, []);

  // 404ページを表示する必要がある場合
  useEffect(() => {
    if (shouldShowNotFound) {
      notFound();
    }
  }, [shouldShowNotFound]);

  // 状態管理
  const [user, setUser] = useState<UserInfo | null>(null);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [isOwnPage, setIsOwnPage] = useState(false);
  const [domain, setDomain] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI状態管理
  const [uiState, setUiState] = useState({
    slideIn: false,
    isModalOpen: false,
    hasInteracted: false,
    openIndex: null as number | null,
    isEditMode: false,
  });

  // ランキングデータ（useCategoryManagementフックで管理されるため削除）
  const [copiedSubcatId, setCopiedSubcatId] = useState<string | null>(null);

  // サブカテゴリの順序管理
  const [orderedSubcategories, setOrderedSubcategories] = useState<any[]>([]);

  // カテゴリの順序管理
  const [orderedCategories, setOrderedCategories] = useState<any[]>([]);

  // サブカテゴリの順序変更ハンドラー
  const handleSubcategoriesReorder = useCallback((newSubcategories: any[]) => {
    setOrderedSubcategories(newSubcategories);
  }, []);

  // カテゴリの順序変更ハンドラー
  const handleCategoriesReorder = useCallback((newCategories: any[]) => {
    setOrderedCategories(newCategories);
    setCategories(newCategories);
  }, []);



  // DnD 用センサー
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        delay: 200, // 200ms長押しでドラッグ開始（クリックとの明確な分離）
        tolerance: 8, // 8pxの許容範囲内での移動は無視
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
      // キーボードナビゲーションの最適化
      keyboardCodes: {
        start: ['Space'],
        cancel: ['Escape'],
        end: ['Space'],
      },
    })
  );

  // 必要なrefを作成
  const headerRef = useRef<HTMLDivElement>(null);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  // ドラッグ&ドロップの状態管理（フックより前に配置）
  const [dragState, setDragState] = useState({
    activeId: null as string | null,
    isDragInProgress: false,
  });

  // useCategoryManagement フックを使用

  const {
    categoryID: selectedCategory,
    selectedCategoryName,
    selectedCategoryIndex,
    subCategory: selectedSubcategory,
    subCategories,
    subcategoryOrder,
    rankingsBySubcategory: managedRankingsBySubcategory,
    forceUpdate,
    isServerUpdating,
    handleCategorySelect,
    handleSubCategorySelect: handleSubcategorySelect,
    resetCategoryState,
    setRankingsBySubcategory: setManagedRankingsBySubcategory,
    setIsServerUpdating,
  } = useCategoryManagement({
    userId,
    pageUserId,
    categories,
    isDragInProgress: dragState.isDragInProgress,
    initializedFromUrl: false,
    updateSubCategory: () => {}, // 簡易実装
    updateCategoryID: () => {}, // 簡易実装
    updateSelectedCategoryName: () => {}, // 簡易実装
    updateSelectedCategoryIndex: () => {}, // 簡易実装
    updateSubCategories: () => {}, // 簡易実装
    updateSubcategoryOrder: () => {}, // 簡易実装
  });

  // ランキングデータ取得完了ログを削除

  // フックから取得したランキングデータを使用
  const finalRankingsBySubcategory = managedRankingsBySubcategory;

  // デバッグ用：finalRankingsBySubcategoryの変更をログ出力
  useEffect(() => {
    console.log('🔍 [FLICKER DEBUG] finalRankingsBySubcategory changed in main page:', {
      keys: Object.keys(finalRankingsBySubcategory),
      totalRankings: Object.values(finalRankingsBySubcategory).reduce((sum, arr) => sum + arr.length, 0),
      selectedCategory,
      isLoading,
      timestamp: new Date().toISOString()
    });
  }, [finalRankingsBySubcategory, selectedCategory, isLoading]);

  // スクロール処理は無効化済み（ScrollManagerに統一）

  // ドラッグ&ドロップフック
  const { handleRankingDragEnd } = useRankingDragDrop({
    pageUserId,
    rankingsBySubcategory: finalRankingsBySubcategory,
    setActiveId: (value: string | null) => setDragState(prev => ({ ...prev, activeId: value })),
    setIsDragInProgress: (value: boolean) => setDragState(prev => ({ ...prev, isDragInProgress: value })),
    setRankingsBySubcategory: setManagedRankingsBySubcategory,
    setIsServerUpdating,
  });

  // ページ遷移関数
  const pageTransit = useCallback(async (uid: string, rid: string) => {

    // 現在のユーザーIDとページのユーザーIDが一致する場合（自分のページを見ている場合）
    // かつ、ログイン状態の場合は編集ページに遷移
    if (isOwnPage && user?.userId) {
      // ログイン中のため編集ページに遷移（新しいURL形式）
      // 現在選択されているカテゴリのcategory_IDを取得
      let categoryParam = '';
      if (selectedCategory) {
        const categoryData = categories.find(cat => cat.id === selectedCategory);
        categoryParam = categoryData?.category_ID ? `&category=${categoryData.category_ID}` : '';
      }

      router.push(`/${pageUsername}/add?ranking_id=${rid}${categoryParam}`);
    } else {
      // それ以外の場合は新しい詳細ページに遷移
      try {
        // ランキングの所有者のユーザー名を取得
        const userResponse = await fetch(`/api/getUser?user_ID=${uid}`);
        if (userResponse.ok) {
          const userData = await userResponse.json();
          const ownerUsername = userData.username;
          if (ownerUsername) {
            // 新しいURL形式で遷移
            router.push(`/${ownerUsername}/post?ranking=${rid}`);
            return;
          }
        }
        // フォールバック: ユーザー名が取得できない場合はホームページに遷移
        router.push('/');
      } catch (error) {
        console.error('ユーザー名取得エラー:', error);
        router.push('/');
      }
    }
  }, [isOwnPage, user?.userId, router, pageUserId, pageUsername, selectedCategory, categories]);

  // SNS並び替えフック
  const {
    handleSnsAndSubcategoryDragStart: snsHandleDragStart,
    handleSnsAndSubcategoryDragEnd: snsHandleDragEnd,
  } = useSnsReordering({
    user,
    userId: pageUserId,
    setUser,
    setActiveId: (value: string | null) => setDragState(prev => ({ ...prev, activeId: value })),
  });

  // 簡単なhandleDragEnd関数
  const handleDragEnd = handleRankingDragEnd;

  // カテゴリ選択のカスタムハンドラー（URL更新付き）
  const handleCategorySelectWithUrl = useCallback(async (catID: string, catName?: string) => {
    // 最新のカテゴリ配列を取得（orderedCategoriesを優先）
    const currentCategories = orderedCategories.length > 0 ? orderedCategories : categories;

    // 元のhandleCategorySelectを呼び出し
    const result = await handleCategorySelect(catID, catName);

    // カテゴリ選択後にURLを更新（最新のcategory_slugを使用）
    setTimeout(() => {
      // 最新のカテゴリ配列から検索
      const latestCategories = orderedCategories.length > 0 ? orderedCategories : categories;
      const selectedCategory = latestCategories.find((c) => String(c.id) === String(catID));

      if (selectedCategory && (selectedCategory.category_slug || selectedCategory.category_ID)) {
        // URL更新：/username?category=slug 形式（slugがない場合はcategory_IDを使用）
        const categoryParam = selectedCategory.category_slug || selectedCategory.category_ID;
        const newUrl = `/${pageUsername}?category=${categoryParam}`;

        // 現在のURLと異なる場合のみ更新（厳密な比較）
        const currentCategoryParam = new URLSearchParams(window.location.search).get('category');
        if (currentCategoryParam !== categoryParam) {
          window.history.pushState(null, '', newUrl);
        }
      }
    }, 100); // カテゴリ選択処理完了後に実行

    return result;
  }, [categories, orderedCategories, pageUsername, handleCategorySelect]);

  // 旧スラッグから新スラッグへのリダイレクトをチェックする関数
  const checkAndRedirectOldSlug = useCallback(async (oldSlug: string): Promise<boolean> => {
    try {
      // デバッグ用：詳細情報を取得


      // 1. データベースのhistory_slugから検索（専用APIを使用）
      try {
        const response = await fetch(`/api/getCategoryByHistorySlug?history_slug=${oldSlug}&user_ID=${pageUserId}`);

        if (response.ok) {
          const data = await response.json();
          const categoryWithHistorySlug = data.category;
          const userData = data.user;

          if (categoryWithHistorySlug && categoryWithHistorySlug.category_slug) {
            // 正しいusernameを使用してリダイレクト
            const correctUsername = userData?.username || pageUsername;
            const newUrl = `/${correctUsername}?category=${categoryWithHistorySlug.category_slug}`;
            window.history.replaceState(null, '', newUrl);
            return true;
          }
        }
      } catch (error) {
        console.error('history_slug検索エラー:', error);
      }

      // 2. フォールバック：ローカルストレージから旧スラッグマッピングを取得
      const slugMappingKey = `slugMapping_${pageUserId}`;
      const storedMapping = localStorage.getItem(slugMappingKey);

      if (storedMapping) {
        const slugMapping = JSON.parse(storedMapping);
        const newSlug = slugMapping[oldSlug];

        if (newSlug && newSlug !== null && newSlug !== 'updating') {
          // 新スラッグが見つかった場合、リダイレクト
          const newUrl = `/${pageUsername}?category=${newSlug}`;
          window.history.replaceState(null, '', newUrl);
          return true;
        }
      }
    } catch (error) {
      console.error('スラッグマッピング確認エラー:', error);
    }
    return false;
  }, [pageUserId, pageUsername]);

  // 統合されたカテゴリ初期化とサブカテゴリ設定処理（パフォーマンス最適化）
  useEffect(() => {
    const initializeCategory = async () => {
      // 初期カテゴリ選択処理
      if (categories.length > 0 && !selectedCategory && !isParamsLoading && pageUsername) {
        // クエリパラメータからcategoryを取得
        const categoryParam = searchParams.get('category');

        let targetCategory = null;

        if (categoryParam) {
          // クエリパラメータで指定されたカテゴリを検索（slugまたはcategory_IDで検索）
          targetCategory = categories.find((cat: any) =>
            cat.category_slug === categoryParam || cat.category_ID === categoryParam
          );

          // 存在しないスラッグの場合の処理
          if (!targetCategory) {
            // 1. 旧スラッグから新スラッグへのリダイレクトを試行
            const redirectResult = await checkAndRedirectOldSlug(categoryParam);
            if (redirectResult) {
              return; // リダイレクト実行済み
            }

            // 2. 仮スラッグ（_temp）の場合、実際のカテゴリを検索
            if (categoryParam.includes('_temp')) {
              // 最近更新されたカテゴリを検索（楽観的更新の可能性）
              const recentlyUpdatedCategory = categories.find((cat: any) => {
                // カテゴリ名から生成される可能性のあるスラッグと比較
                if (cat.category_name) {
                  const expectedTempSlug = `category_${pageUserId}_temp`;
                  return categoryParam === expectedTempSlug;
                }
                return false;
              });

              if (recentlyUpdatedCategory) {
                targetCategory = recentlyUpdatedCategory;
                // 正しいスラッグでURLを更新
                const correctSlug = recentlyUpdatedCategory.category_slug || recentlyUpdatedCategory.category_ID;
                const newUrl = `/${pageUsername}?category=${correctSlug}`;
                window.history.replaceState(null, '', newUrl);
              }
            }
          }
        }

        if (!targetCategory) {
          // クエリパラメータがない、または指定されたカテゴリが見つからない場合
          if (categoryParam) {
            // 最後の手段：データベースから最新情報を取得
            try {
              console.log('🔍 最新カテゴリ情報を取得中...', categoryParam);
              const response = await fetch(`/api/getCategories?user_ID=${pageUserId}`);
              if (response.ok) {
                const latestCategories = await response.json();
                console.log('🔍 最新カテゴリ情報取得完了:', latestCategories.length, '件');

                console.log('🔍 最新データ詳細:');
                latestCategories.forEach((cat: any, index: number) => {
                  console.log(`  [${index}] ID: ${cat.id}`);
                  console.log(`      名前: "${cat.category_name}"`);
                  console.log(`      現在のslug: "${cat.category_slug}"`);
                  console.log(`      履歴slug: "${cat.history_slug}"`);
                  console.log(`      category_ID: "${cat.category_ID}"`);
                  console.log('      ---');
                });

                console.log(`🔍 検索対象: "${categoryParam}"`);
                console.log('🔍 slug/category_ID比較:');
                latestCategories.forEach((cat: any, index: number) => {
                  const slugMatch = cat.category_slug === categoryParam;
                  const idMatch = cat.category_ID === categoryParam;
                  console.log(`  [${index}] slug:"${cat.category_slug}" === "${categoryParam}" → ${slugMatch}`);
                  console.log(`  [${index}] ID:"${cat.category_ID}" === "${categoryParam}" → ${idMatch}`);
                });

                // 最新データで再検索
                targetCategory = latestCategories.find((cat: any) =>
                  cat.category_slug === categoryParam || cat.category_ID === categoryParam
                );

                if (targetCategory) {
                  console.log('✅ 最新データでカテゴリが見つかりました:', targetCategory.category_name);
                  // 最新データでカテゴリ状態を更新
                  setCategories(latestCategories);
                } else {
                  console.log('❌ 最新データでもカテゴリが見つかりません:', categoryParam);
                }
              }
            } catch (error) {
              console.error('最新カテゴリ情報の取得に失敗:', error);
            }

            // それでも見つからない場合は404表示
            if (!targetCategory) {
              console.error('❌ 指定されたカテゴリが見つかりません:', categoryParam);
              setShouldShowNotFound(true);
              return;
            }
          } else {
            // order値でソートして最上位（order=0）のカテゴリを特定
            const sortedCategories = [...categories].sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
            targetCategory = sortedCategories[0];
          }
        }

      if (targetCategory) {
        // カテゴリを選択（初回アクセス時はURL更新なしで直接リダイレクト）
        if (!categoryParam) {
          // クエリパラメータがない場合は、URL更新なしでカテゴリ選択してから直接リダイレクト
          setTimeout(() => {
            handleCategorySelect(targetCategory.id, targetCategory.category_name);
          }, 0);

          // 直接最終URLにリダイレクト（中間のリダイレクトを避ける）
          const categoryParam = targetCategory.category_slug || targetCategory.category_ID;
          const finalUrl = `/${pageUsername}?category=${categoryParam}`;
          window.history.replaceState(null, '', finalUrl);
        } else {
          // クエリパラメータがある場合はURL更新なしでカテゴリ選択
          setTimeout(() => {
            handleCategorySelect(targetCategory.id, targetCategory.category_name);
          }, 0);
        }
        return;
      }

      // フォールバック：カテゴリが存在しない場合は404
      if (categories.length === 0) {
        console.error('❌ カテゴリが存在しません');
        setShouldShowNotFound(true);
      }
    }

    // カテゴリが変更された時にサブカテゴリの順序を初期化（統合処理）
    if (selectedCategory && categories.length > 0) {
        const currentCategory = categories.find(cat => cat.id === selectedCategory);
        if (currentCategory?.subcategories) {
          // order値でソートしてサブカテゴリの順序を設定
          const sortedSubcategories = [...currentCategory.subcategories].sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
          setOrderedSubcategories(sortedSubcategories);
        }
      }
    };

    initializeCategory();
  }, [categories, selectedCategory, isParamsLoading, pageUsername, searchParams, checkAndRedirectOldSlug]);

  // カテゴリが変更された時にカテゴリの順序を初期化
  useEffect(() => {
    if (categories.length > 0) {
      // order値でソートしてカテゴリの順序を設定
      const sortedCategories = [...categories].sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
      setOrderedCategories(sortedCategories);
    }
  }, [categories]);

  // 楽観的更新中のカテゴリIDを追跡
  const [optimisticUpdateCategoryId, setOptimisticUpdateCategoryId] = useState<string | null>(null);

  // カテゴリデータを再取得する関数
  const refreshCategories = useCallback(async (skipOptimisticProtection = false) => {
    if (!pageUserId) return;

    try {
      const response = await fetch(`/api/getCategories?user_ID=${pageUserId}`);
      if (response.ok) {
        const categoriesData = await response.json();

        // 楽観的更新中のカテゴリがある場合、そのカテゴリの更新をスキップ
        if (optimisticUpdateCategoryId && !skipOptimisticProtection) {
          console.log('🛡️ 楽観的更新保護中:', optimisticUpdateCategoryId);
          setCategories(prevCategories => {
            const protectedCategories = categoriesData.map((newCat: any) => {
              if (newCat.id === optimisticUpdateCategoryId) {
                // 楽観的更新中のカテゴリは既存の値を保持
                const existingCat = prevCategories.find(cat => cat.id === optimisticUpdateCategoryId);
                if (existingCat) {
                  console.log('🛡️ カテゴリ保護:', {
                    categoryId: optimisticUpdateCategoryId,
                    existingName: existingCat.category_name,
                    newName: newCat.category_name,
                    action: '既存値を保持'
                  });
                  return existingCat;
                }
              }
              return newCat;
            });
            return protectedCategories;
          });
        } else {
          console.log('📥 カテゴリデータ更新:', categoriesData.length, '件');
          setCategories(categoriesData);
        }

        // sessionStorageのキャッシュも更新
        const cachedData = sessionStorage.getItem('cachedUserData');
        if (cachedData) {
          try {
            const parsedCache = JSON.parse(cachedData);
            parsedCache.categories = categoriesData;
            parsedCache.timestamp = Date.now(); // タイムスタンプも更新
            sessionStorage.setItem('cachedUserData', JSON.stringify(parsedCache));
          } catch (cacheError) {
            // キャッシュ更新エラーは無視
          }
        }
      }
    } catch (error) {
      console.error('カテゴリデータの再取得に失敗しました:', error);
    }
  }, [pageUserId, optimisticUpdateCategoryId]);

  // カテゴリ更新時のコールバック
  const handleCategoryUpdated = useCallback(async (action: string, categoryId: string, categoryName?: string) => {
    if ((action === 'edit' || action === 'edit_success') && categoryId && categoryName) {
      if (action === 'edit') {
        // 楽観的更新開始：カテゴリIDを保護対象に設定
        setOptimisticUpdateCategoryId(categoryId);

        // 旧スラッグを記録（フォールバック用）- メインはデータベースのhistory_slug
        const oldCategory = categories.find(cat => cat.id === categoryId);
        if (oldCategory && oldCategory.category_slug) {
          console.log('📝 カテゴリ名変更:', {
            categoryId,
            oldName: oldCategory.category_name,
            newName: categoryName,
            oldSlug: oldCategory.category_slug
          });

          // データベースのhistory_slugがメインなので、ローカルストレージは補助的に使用
          const slugMappingKey = `slugMapping_${pageUserId}`;
          try {
            const storedMapping = localStorage.getItem(slugMappingKey);
            const slugMapping = storedMapping ? JSON.parse(storedMapping) : {};

            // 旧スラッグをキーとして保存（新スラッグは後でサーバーから取得）
            slugMapping[oldCategory.category_slug] = null;
            localStorage.setItem(slugMappingKey, JSON.stringify(slugMapping));
          } catch (error) {
            console.error('スラッグマッピング保存エラー:', error);
          }
        }
      }

      // 編集の場合：楽観的更新でカテゴリ名を即座に更新
      // category_slugは変更せず、旧スラッグを保持（URLが変わらないように）
      setCategories(prevCategories =>
        prevCategories.map(cat => {
          if (cat.id === categoryId) {
            return {
              ...cat,
              category_name: categoryName,
              // category_slugは変更しない（旧スラッグを保持してURLを安定させる）
            };
          }
          return cat;
        })
      );

      // 選択中のカテゴリが更新された場合、カテゴリ選択状態も更新
      if (selectedCategory === categoryId) {
        handleCategorySelect(categoryId, categoryName);

        // 楽観的更新時はURLを変更しない（旧URLを保持）
        // 新しいスラッグが取得できたタイミングでURL更新
      }

      // バックグラウンドでデータを再取得（整合性確保）
      if (action === 'edit_success') {
        // API成功後は楽観的更新保護を解除してデータを再取得
        console.log('✅ API成功：楽観的更新保護を解除', categoryId);
        setOptimisticUpdateCategoryId(null);
        setTimeout(async () => {
          console.log('🔄 完全データ更新開始');
          await refreshCategories(true); // 保護をスキップして完全更新

          // 正確なcategory_slugでURLとスラッグマッピングを更新
          console.log('🔍 [handleCategoryUpdated] URL更新条件チェック:', {
            selectedCategory,
            categoryId,
            isMatch: selectedCategory === categoryId,
            selectedCategoryType: typeof selectedCategory,
            categoryIdType: typeof categoryId
          });

          if (selectedCategory === categoryId) {
            console.log('✅ [handleCategoryUpdated] URL更新条件満たされました');
            // refreshCategories完了後に最新データを取得してURL更新
            try {
              console.log('🔄 [handleCategoryUpdated] カテゴリデータ取得開始:', `/api/getCategories?user_ID=${pageUserId}`);
              const response = await fetch(`/api/getCategories?user_ID=${pageUserId}`);
              console.log('🔄 [handleCategoryUpdated] レスポンス受信:', {
                status: response.status,
                ok: response.ok,
                statusText: response.statusText
              });

              if (response.ok) {
                const data = await response.json();
                console.log('🔄 [handleCategoryUpdated] データ解析結果:', {
                  isArray: Array.isArray(data),
                  categoriesLength: data?.length,
                  categories: data
                });

                // /api/getCategories は配列を直接返す
                if (Array.isArray(data)) {
                  console.log('🔄 [handleCategoryUpdated] 全カテゴリデータ:', data.map(cat => ({
                    id: cat.id,
                    name: cat.category_name,
                    slug: cat.category_slug
                  })));

                  const updatedCategory = data.find((cat: any) => cat.id === categoryId);
                  console.log('🔄 [handleCategoryUpdated] 更新されたカテゴリ検索:', {
                    categoryId,
                    foundCategory: updatedCategory,
                    categorySlug: updatedCategory?.category_slug
                  });

                  if (updatedCategory && updatedCategory.category_slug) {
                    const newUrl = `/${pageUsername}?category=${updatedCategory.category_slug}`;
                    const currentUrl = window.location.href;

                    console.log('🔄 [handleCategoryUpdated] URL比較:', {
                      currentUrl,
                      newUrl,
                      newSlug: updatedCategory.category_slug,
                      includes: currentUrl.includes(`category=${updatedCategory.category_slug}`)
                    });

                    // 現在のURLと異なる場合のみ更新（厳密な比較）
                    const currentCategoryParam = new URLSearchParams(window.location.search).get('category');
                    if (currentCategoryParam !== updatedCategory.category_slug) {
                      console.log('🔄 [handleCategoryUpdated] URL更新実行:', {
                        from: currentUrl,
                        to: newUrl,
                        newSlug: updatedCategory.category_slug,
                        currentParam: currentCategoryParam,
                        newParam: updatedCategory.category_slug
                      });
                      window.history.replaceState(null, '', newUrl);
                    } else {
                      console.log('⏭️ [handleCategoryUpdated] URL更新スキップ（既に同じURL）');
                    }
                  } else {
                    console.log('❌ [handleCategoryUpdated] カテゴリまたはスラッグが見つかりません:', {
                      updatedCategory,
                      hasSlug: !!updatedCategory?.category_slug
                    });
                  }
                } else {
                  console.log('❌ [handleCategoryUpdated] データ形式エラー（配列ではない）:', data);
                }
              } else {
                console.log('❌ [handleCategoryUpdated] レスポンスエラー:', response.status, response.statusText);
              }

              // ローカルストレージのマッピングをクリーンアップ
              const slugMappingKey = `slugMapping_${pageUserId}`;
              try {
                const storedMapping = localStorage.getItem(slugMappingKey);
                if (storedMapping) {
                  const slugMapping = JSON.parse(storedMapping);
                  // 仮スラッグエントリを削除（データベースのhistory_slugがメインになるため）
                  Object.keys(slugMapping).forEach(oldSlug => {
                    if (slugMapping[oldSlug].includes('_temp')) {
                      delete slugMapping[oldSlug];
                      console.log('🗑️ 仮スラッグマッピング削除:', oldSlug);
                    }
                  });
                  localStorage.setItem(slugMappingKey, JSON.stringify(slugMapping));
                  console.log('💾 スラッグマッピングクリーンアップ完了');
                }
              } catch (error) {
                console.error('スラッグマッピングクリーンアップエラー:', error);
              }
            } catch (error) {
              console.error('❌ [handleCategoryUpdated] URL更新用データ取得エラー:', error);
            }
          } else {
            console.log('❌ [handleCategoryUpdated] URL更新条件満たされず:', {
              selectedCategory,
              categoryId,
              reason: 'selectedCategory !== categoryId'
            });
          }

          // SimpleUserInitializerのrefreshCategories関数も呼び出し
          if (typeof window !== 'undefined' && (window as any).refreshCategories) {
            await (window as any).refreshCategories();
          }
        }, 100); // 短い遅延で実行
      } else {
        // 楽観的更新中はバックグラウンド更新をスキップ
        // edit_success時の完全更新のみで十分
        console.log('⏰ 楽観的更新中のため、バックグラウンド更新をスキップ');
      }
    } else {
      // 追加・削除の場合：従来通りデータを再取得
      await refreshCategories();

      // SimpleUserInitializerのrefreshCategories関数も呼び出し
      if (typeof window !== 'undefined' && (window as any).refreshCategories) {
        await (window as any).refreshCategories();
      }

      // 追加の場合は新しいカテゴリを選択
      if (action === 'add' && categoryId) {
        // 少し遅延を入れてからカテゴリを選択（データ更新を確実にするため）
        setTimeout(() => {
          const updatedCategory = categories.find(cat => cat.id === categoryId);
          if (updatedCategory) {
            setTimeout(() => {
              handleCategorySelect(categoryId, categoryName || updatedCategory.category_name);
            }, 0);
          }
        }, 100);
      }
    }
  }, [refreshCategories, categories, selectedCategory, handleCategorySelect]);

  // ユーザーデータの処理
  const handleUserDataReady = (data: {
    user: UserInfo | null;
    categories: CategoryType[];
    isOwnPage: boolean;
    domain: string;
  }) => {
    console.log('🔍 [FLICKER DEBUG] handleUserDataReady called:', {
      hasUser: !!data.user,
      categoriesCount: data.categories.length,
      isOwnPage: data.isOwnPage,
      domain: data.domain,
      currentIsLoading: isLoading,
      timestamp: new Date().toISOString()
    });

    // 既存のユーザーデータと比較して、ドラッグ&ドロップによる変更を保持
    if (user && data.user && (user as any)._lastSnsUpdate && !(data.user as any)._lastSnsUpdate) {
      // 既存のユーザーデータにドラッグ&ドロップの変更がある場合は、それを保持
      data.user = {
        ...data.user,
        snsOrder: user.snsOrder,
        _lastSnsUpdate: (user as any)._lastSnsUpdate
      } as any;
    }

    // LCP要素（プロフィール画像）の最優先プリロード
    if (data.user?.profile_image) {
      const profileImg = new window.Image();
      profileImg.src = data.user.profile_image;
      // 高優先度でプリロード
      profileImg.loading = 'eager';
      profileImg.fetchPriority = 'high';

      // DOM要素にrel="preload"を追加
      const preloadLink = document.createElement('link');
      preloadLink.rel = 'preload';
      preloadLink.as = 'image';
      preloadLink.href = data.user.profile_image;
      preloadLink.fetchPriority = 'high';
      document.head.appendChild(preloadLink);
    }

    // 背景画像の事前読み込み（LCP最適化）
    if (data.user?.background_image) {
      const img = new window.Image();
      img.src = data.user.background_image;
      img.loading = 'eager';
    }

    // 状態を一括で更新してちらつきを防ぐ
    // startTransitionを使用して優先度を制御
    console.log('🔍 [FLICKER DEBUG] About to update states:', {
      willSetUser: !!data.user,
      willSetCategories: data.categories.length,
      willSetIsOwnPage: data.isOwnPage,
      willSetDomain: data.domain,
      currentIsLoading: isLoading,
      timestamp: new Date().toISOString()
    });

    startTransition(() => {
      setUser(data.user);
      setCategories(data.categories);
      setIsOwnPage(data.isOwnPage);
      setDomain(data.domain);
    });

    // ローディング状態を最後に更新（即座に反映）
    console.log('🔍 [FLICKER DEBUG] Setting isLoading to false');
    setIsLoading(false);

    // SNSアイコンの超高速プリロード（ユーザーデータ取得後）
    if (data.user?.snsLinks) {
      const preloadSnsIcons = async () => {
        // getSnsIconSrc関数を動的インポート
        const { getSnsIconSrc } = await import('../mypageRelease/[user_ID]/utils/snsUtils');

        const snsTypes = data.user.snsOrder || Object.keys(data.user.snsLinks);
        snsTypes.forEach((snsType) => {
          if (data.user.snsLinks[snsType]) {
            const iconSrc = getSnsIconSrc(snsType);

            // 高優先度でプリロード
            const preloadLink = document.createElement('link');
            preloadLink.rel = 'preload';
            preloadLink.as = 'image';
            preloadLink.href = iconSrc;
            document.head.appendChild(preloadLink);
          }
        });
      };

      // 即座に実行
      requestAnimationFrame(preloadSnsIcons);
    }


  };

  const handleError = (errorMessage: string) => {
    console.error('❌ メインページ: エラー受信', errorMessage);
    setError(errorMessage);
    setIsLoading(false);
  };

  // イベントハンドラー
  const handleUrlClick = () => {
    // アコーディオンの開閉
    setUiState(prev => ({ ...prev, slideIn: !prev.slideIn }));
  };

  const handleMenuClick = () => {
    setUiState(prev => ({ ...prev, isModalOpen: !prev.isModalOpen }));
  };

  const handleCloseModal = () => {
    setUiState(prev => ({ ...prev, isModalOpen: false }));
  };



  // ランキング編集機能
  const handleEditRanking = useCallback(async (rankingId: string) => {
    if (!pageUsername) return;

    try {
      // カテゴリ情報を取得してURLパラメータに追加
      let categoryParam = '';
      if (selectedCategory) {
        const categoryData = categories.find(cat => cat.id === selectedCategory);
        const categorySlugOrId = categoryData?.category_slug || categoryData?.category_ID;
        categoryParam = categorySlugOrId ? `&category=${categorySlugOrId}` : '';
      }


      router.push(`/${pageUsername}/add?ranking_id=${rankingId}${categoryParam}`);
    } catch (error) {
      console.error('編集ページ遷移エラー:', error);
    }
  }, [pageUsername, selectedCategory, categories, router]);

  // ランキング削除機能
  const handleDeleteRanking = useCallback(async (rankingId: string) => {
    if (!pageUserId) return;

    try {
      const response = await fetch('/api/deleteRanking', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ranking_ID: rankingId,
          user_ID: pageUserId,
        }),
      });

      if (response.ok) {
        // 削除成功時にランキングリストを更新
        console.log('🔍 [FLICKER DEBUG] handleDeleteRanking - deleting ranking:', rankingId);
        setManagedRankingsBySubcategory(prev => {
          const updated = { ...prev };
          console.log('🔍 [FLICKER DEBUG] handleDeleteRanking - before deletion:', {
            keys: Object.keys(prev),
            totalRankings: Object.values(prev).reduce((sum, arr) => sum + arr.length, 0),
            data: prev
          });

          Object.keys(updated).forEach(subcatId => {
            const beforeLength = updated[subcatId].length;
            updated[subcatId] = updated[subcatId].filter(ranking => ranking.ranking_ID !== rankingId);
            const afterLength = updated[subcatId].length;

            if (beforeLength !== afterLength) {
              console.log('🔍 [FLICKER DEBUG] handleDeleteRanking - deleted from subcat:', {
                subcatId,
                beforeLength,
                afterLength,
                deletedRankingId: rankingId
              });
            }
          });

          console.log('🔍 [FLICKER DEBUG] handleDeleteRanking - after deletion:', {
            keys: Object.keys(updated),
            totalRankings: Object.values(updated).reduce((sum, arr) => sum + arr.length, 0),
            data: updated
          });

          return updated;
        });

      } else {
        const errorData = await response.json();
        console.error('ランキング削除エラー:', errorData);
      }
    } catch (error) {
      console.error('ランキング削除中にエラーが発生しました:', error);
    }
  }, [pageUserId]);

  // ランキング共有機能
  const handleShareRanking = useCallback(async (rankingId: string, title: string, description?: string, ownerId?: string) => {
    if (typeof window === 'undefined') return;

    try {
      let shareUrl = '';

      if (ownerId) {
        // ランキング所有者のユーザー名を取得
        const userResponse = await fetch(`/api/getUser?user_ID=${ownerId}`);
        if (userResponse.ok) {
          const userData = await userResponse.json();
          const ownerUsername = userData.username;
          if (ownerUsername) {
            shareUrl = `${window.location.origin}/${ownerUsername}/post?ranking=${rankingId}`;
          }
        }
      }

      // フォールバック: ユーザー名が取得できない場合は現在のページのユーザー名を使用
      if (!shareUrl) {
        shareUrl = `${window.location.origin}/${pageUsername}/post?ranking=${rankingId}`;
      }

      const shareTitle = "ランキングを共有する";
      const shareText = description
        ? `「${title}」のおすすめの理由を見てください！\n${description.substring(0, 100)}${description.length > 100 ? '...' : ''}`
        : `「${title}」のおすすめの理由を見てください！`;

      if (navigator.share) {
        try {
          await navigator.share({
            title: shareTitle,
            text: shareText,
            url: shareUrl
          });
        } catch (error) {
          console.error('共有に失敗しました:', error);
        }
      } else {
        // ネイティブ共有が対応していない場合はURLをコピー
        try {
          await navigator.clipboard.writeText(shareUrl);
          // URLをクリップボードにコピー完了
        } catch (error) {
          // URLのコピーに失敗した場合は何もしない
        }
      }
    } catch (error) {
      console.error('共有URL生成エラー:', error);
    }
  }, [pageUsername]);



  // SNSドラッグ&ドロップハンドラー（useSnsReorderingフックのものを使用）
  const handleSnsAndSubcategoryDragStart = snsHandleDragStart;
  const handleSnsAndSubcategoryDragEnd = snsHandleDragEnd;

  // デバッグログを削除



  // エラー時の表示
  if (error) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md min-h-screen flex items-center justify-center">
            <div className="text-center w-full px-4">
              <h1 className="text-[20px] font-semibold text-[#313131] mb-4">
                このページはご利用いただけません。
              </h1>
              <p className="text-[#676767] mb-8">
                リンクに問題があるか、ページが削除された可能性があります。
              </p>
              <button
                onClick={() => router.push('/')}
                className="inline-block w-[340px] h-[40px] bg-[#FFD814] text-[#313131] font-medium rounded-[100px] hover:bg-[#E1BC03] transition-colors leading-[40px]"
              >
                ホームに戻る
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }



  // メインレンダリングログを削除

  return (
    <BrowserExtensionProtectionProvider>
      <div className="relative min-h-screen flex flex-col items-center overflow-x-hidden" style={{ maxWidth: "500px", margin: "0 auto" }}>
        {/* SimpleUserInitializerはpageUserIdが設定された場合のみ実行 */}
        {pageUserId && (
          <SimpleUserInitializer
            pageUserId={pageUserId}
            onUserDataReady={handleUserDataReady}
            onError={handleError}
          />
        )}

      {/* 白背景（body のグラデーションを隠す） */}
      <div
        className="fixed z-[-30] w-full"
        style={{
          top: "0",
          bottom: "0",
          left: "0",
          right: "0",
          height: "100vh",
          maxWidth: "500px",
          margin: "0 auto"
        }}
      />

      {/* 背景画像 */}
      <div
        className="fixed z-[-20] w-full"
        style={{
          top: "0",
          bottom: "0",
          left: "0",
          right: "0",
          height: "100vh",
          backgroundImage: user?.background_image ? `url(${user.background_image})` : (user as any)?.backgroundImage ? `url(${(user as any).backgroundImage})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          maxWidth: "500px",
          margin: "0 auto"
        }}
      />
      <div
        className="fixed z-[-10] w-full bg-black/70"
        style={{
          top: "0",
          bottom: "0",
          left: "0",
          right: "0",
          height: "100vh",
          maxWidth: "500px",
          margin: "0 auto"
        }}
      />



      {/* ユーザーデータが取得できた場合のみ表示 */}
      {user && pageUserId ? (
        <>

          <SideMenu
            isModalOpen={uiState.isModalOpen}
            handleCloseModal={handleCloseModal}
            isLoggedIn={!!user}
            handleTouchStart={() => {}}
            handleTouchMove={() => {}}
            handleTouchEnd={() => {}}
          />

          {/* 三点メニュー */}
          <div className="w-full relative pt-4">
            <button
              className="absolute top-4 right-4 w-8 h-8 flex flex-col items-center justify-center z-10"
              onClick={handleMenuClick}
              aria-label="プロフィールメニュー"
            >
              <div className="w-1 h-1 bg-white rounded-full mb-1" />
              <div className="w-1 h-1 bg-white rounded-full mb-1" />
              <div className="w-1 h-1 bg-white rounded-full" />
            </button>
          </div>

          <ProfileHeader
            user={user}
            pageUserId={pageUserId}
            domain={pageUsername}
            isOwnPage={isOwnPage}
            onUrlClick={handleUrlClick}
          />

          <ProfileDetails
            user={user}
            slideIn={uiState.slideIn}
            setSlideIn={(value: boolean) => setUiState(prev => ({ ...prev, slideIn: value }))}
            isOwnPage={isOwnPage}
            handleSnsAndSubcategoryDragStart={(e) => {
              requestAnimationFrame(() => {
                handleSnsAndSubcategoryDragStart(e);
              });
            }}
            handleSnsAndSubcategoryDragEnd={(e) => {
              requestAnimationFrame(() => {
                handleSnsAndSubcategoryDragEnd(e);
              });
            }}
            sensors={sensors}
          />

          {!uiState.slideIn && (
            <div
              className="mt-[36px] w-full bg-white rounded-t-md"
              style={{
                height: "auto",
                flex: "1",
                display: "flex",
                flexDirection: "column",
                overflow: "visible",
                transition: "height 0.3s ease-in-out"
              }}
            >
              <div style={{ display: "flex", flexDirection: "column", width: "100%", flex: "1" }}>
                <CategoryTabs
                tabsContainerRef={tabsContainerRef}
                categoryTabRef={headerRef}
                userId={userId}
                pageUserId={pageUserId}
                username={pageUsername}
                categories={orderedCategories.length > 0 ? orderedCategories : categories}
                subCategories={(orderedCategories.length > 0 ? orderedCategories : categories).find(cat => cat.id === selectedCategory)?.subcategories || []}
                selectedCategoryIndex={(orderedCategories.length > 0 ? orderedCategories : categories).findIndex(cat => cat.id === selectedCategory)}
                categoryID={selectedCategory}
                subCategory={selectedSubcategory}
                isOwnPage={isOwnPage}
                forceUpdate={0}
                handleCategorySelect={handleCategorySelectWithUrl}
                handleSubCategorySelect={handleSubcategorySelect}
                resetCategoryState={resetCategoryState}
                handleSubcategoriesReorder={handleSubcategoriesReorder}
                isEditMode={true}
                isModalOpen={false}
                onModalClose={() => {}}
                selectedCategoryName={(orderedCategories.length > 0 ? orderedCategories : categories).find(cat => cat.id === selectedCategory)?.category_name || ""}
                onCategoriesReorder={handleCategoriesReorder}
                onCategoryUpdated={handleCategoryUpdated}
                rankingsBySubcategory={finalRankingsBySubcategory}
                orderedSubcategories={orderedSubcategories}
                />

                {/* ランキング一覧 */}
              <div className="flex flex-col items-center p-4 pt-0 w-full flex-1 overflow-y-auto">
                {selectedCategory ? (
                  <div className="w-full">
                    {orderedSubcategories.map((subcat) => {
                      const subcatRankings = finalRankingsBySubcategory[subcat.id] || [];

                      // ランキング数が0の場合の表示制御を最適化
                      // データが存在しない場合のみ非表示（初期ロード中やデータ取得中は表示を継続）
                      const shouldHide = subcatRankings.length === 0 &&
                        !isLoading &&
                        Object.keys(finalRankingsBySubcategory).length > 0;

                      console.log('🔍 [FLICKER DEBUG] Subcat render check:', {
                        subcatId: subcat.id,
                        subcatName: subcat.category_name,
                        rankingsLength: subcatRankings.length,
                        shouldHide,
                        isLoading,
                        finalRankingsKeys: Object.keys(finalRankingsBySubcategory),
                        timestamp: new Date().toISOString()
                      });

                      if (shouldHide) return null;

                      return (
                        <div
                          key={subcat.id}
                          id={`subcat-section-${subcat.id}`}
                          data-subcat-id={subcat.id}
                          className="ranking-section mb-5"
                        >
                          {/* サブカテゴリ見出し */}
                          <div className="flex items-center mb-1.5">
                            <h2 className="text-base font-bold text-gray-800">
                              {subcat.category_name}のおすすめ
                            </h2>
                            <button
                              onClick={async () => {
                                if (typeof window === 'undefined') return;

                                // 現在選択されているカテゴリのcategory_IDを取得
                                const currentCategory = (orderedCategories.length > 0 ? orderedCategories : categories).find(cat => cat.id === selectedCategory);
                                const categoryIdForUrl = currentCategory?.category_ID || selectedCategory;
                                const url = `${window.location.origin}/${pageUsername}?category=${categoryIdForUrl}&subcategory=${subcat.id}`;
                                const title = "サブカテゴリを共有する";
                                const text = `「${subcat.category_name}のおすすめ」をチェックする！`;

                                if (navigator.share) {
                                  navigator.share({ title, text, url }).catch(() => {});
                                } else {
                                  navigator.clipboard.writeText(url)
                                    .then(() => {
                                      setCopiedSubcatId(subcat.id);
                                      setTimeout(() => setCopiedSubcatId(null), 2000);
                                    })
                                    .catch(() => {});
                                }
                              }}
                              className="flex items-center justify-center w-8 h-8 transition-colors duration-200"
                              title={copiedSubcatId === subcat.id ? "URLをコピーしました" : "共有"}
                            >
                              {copiedSubcatId === subcat.id ? (
                                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              ) : (
                                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                              )}
                            </button>
                          </div>

                          {/* ランキングリスト */}
                          <RankingList
                            rankings={subcatRankings}
                            isOwnPage={isOwnPage}
                            isEditMode={uiState.isEditMode}
                            subcatId={subcat.id}
                            userId={pageUserId}
                            pageTransit={pageTransit}
                            sensors={sensors}
                            onShare={handleShareRanking}
                            onDragStart={() => {}}
                            onDragEnd={handleDragEnd}
                            showEmptyState={false}
                            onEdit={handleEditRanking}
                            onDelete={handleDeleteRanking}
                            isLoading={isLoading}
                          />
                        </div>
                      );
                    })}
                  </div>
                ) : null}
                </div>

                <Footer className="!flex-[0_0_auto] mt-auto w-full" />
              </div>
            </div>
          )}


        </>
      ) : (
        // ユーザーデータ読み込み中のローディング表示
        <div
          className="mt-[36px] w-full bg-white rounded-t-md"
          style={{
            height: "auto",
            flex: "1",
            display: "flex",
            flexDirection: "column",
            overflow: "visible",
            transition: "height 0.3s ease-in-out"
          }}
        >
          <div className="flex-1 flex items-center justify-center">
            <CatLoadingAnimation />
          </div>
        </div>
      )}

      {/* 404表示 */}
      {shouldShowNotFound && (
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">カテゴリが見つかりません</h2>
          <p className="text-gray-600 mb-6">指定されたカテゴリは存在しないか、削除された可能性があります。</p>
          <button
            onClick={() => {
              setShouldShowNotFound(false);
              const newUrl = `/${pageUsername}`;
              window.history.replaceState(null, '', newUrl);
              window.location.reload();
            }}
            className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            トップページに戻る
          </button>
        </div>
      )}

      </div>
    </BrowserExtensionProtectionProvider>
  );
}
