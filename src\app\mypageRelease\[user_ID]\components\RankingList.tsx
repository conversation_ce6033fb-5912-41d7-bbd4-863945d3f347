import React, { useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { DraggableRankingItem } from "@/components/DraggableRankingItem/DraggableRankingItem";
import type { RankingType } from "../types";

interface RankingListProps {
  rankings: RankingType[];
  isOwnPage: boolean;
  isEditMode: boolean;
  subcatId: string;
  userId: string;
  pageTransit: (userId: string, rankingId: string) => void;
  sensors: any; // Should be properly typed based on DnD kit
  onShare: (rankingId: string, title: string, description?: string, ownerId?: string) => void;
  onDragStart: (event: DragStartEvent) => void;
  onDragEnd: (event: DragEndEvent) => void;
  showEmptyState?: boolean;
  onEdit?: (rankingId: string) => void;
  onDelete?: (rankingId: string) => void;
  isLoading?: boolean;
}

export const RankingList: React.FC<RankingListProps> = ({
  rankings,
  isOwnPage,
  isEditMode,
  subcatId,
  userId,
  pageTransit,
  sensors,
  onShare,
  onDragStart,
  onDragEnd,
  showEmptyState = true,
  onEdit,
  onDelete,
  isLoading = false,
}) => {
  // ドラッグ制約用のref
  const containerRef = useRef<HTMLDivElement>(null);
  // ランキング数が0の場合（安全性チェック追加）
  // ローディング中は空状態を表示しない（ちらつき防止）
  if (!rankings || rankings.length === 0) {
    // ローディング中は何も表示しない（親コンポーネントで制御）
    if (isLoading) return null;

    if (!showEmptyState) return null;

    // データが確実に存在しない場合のみ空状態を表示
    return (
      <div className="flex flex-col items-center p-8 text-gray-500">
        <div className="text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
          <h3 className="text-sm font-medium text-gray-900 mb-2">ランキングがありません</h3>
          {isOwnPage && (
            <p className="text-sm text-gray-500">
              最初のランキングを作成してください
            </p>
          )}
        </div>
      </div>
    );
  }

  // ランキングがある場合の表示
  if (isOwnPage) {
    // オーナーの場合：ドラッグ可能
    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        modifiers={[restrictToVerticalAxis, restrictToParentElement]}
      >
        <SortableContext
          items={(rankings || []).map(r => r.ranking_ID)}
          strategy={verticalListSortingStrategy}
        >
          <div
            ref={containerRef}
            className="space-y-4 mt-1 overflow-x-hidden"
            style={{ position: 'relative' }}
          >
            {(rankings || []).map((ranking, idx) => (
              <DraggableRankingItem
                key={ranking.ranking_ID}
                ranking={ranking}
                index={idx}
                subcatId={subcatId}
                isEditMode={isEditMode}
                userId={userId}
                pageTransit={pageTransit}
                onShare={(rankingId, title, description) => onShare(rankingId, title, description, ranking.user_id)}
                isDraggable={true}
                isOwnPage={isOwnPage}
                onEdit={onEdit}
                onDelete={onDelete}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    );
  } else {
    // 訪問者の場合：ドラッグ不可
    return (
      <div className="space-y-4">
        {(rankings || []).map((ranking, idx) => (
          <DraggableRankingItem
            key={ranking.ranking_ID}
            ranking={ranking}
            index={idx}
            subcatId={subcatId}
            isEditMode={false}
            userId={userId}
            pageTransit={pageTransit}
            onShare={(rankingId, title, description) => onShare(rankingId, title, description, ranking.user_id)}
            isDraggable={false}
            isOwnPage={isOwnPage}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
      </div>
    );
  }
};