import { useCallback, useState, useEffect, useMemo, useRef } from "react";
import type { CategoryType, RankingType } from "../types";

interface UseCategoryManagementProps {
  userId: string;
  pageUserId: string;
  categories: CategoryType[];
  isDragInProgress: boolean;
  initializedFromUrl: boolean;
  updateSubCategory: (id: string) => void;
  updateCategoryID: (id: string) => void;
  updateSelectedCategoryName: (name: string) => void;
  updateSelectedCategoryIndex: (index: number) => void;
  updateSubCategories: (subcategories: CategoryType[]) => void;
  updateSubcategoryOrder: (order: string[]) => void;
}

interface UseCategoryManagementReturn {
  categoryID: string;
  selectedCategoryName: string;
  selectedCategoryIndex: number;
  subCategory: string;
  subCategories: CategoryType[];
  subcategoryOrder: string[];
  rankingsBySubcategory: Record<string, RankingType[]>;
  forceUpdate: number;
  ignoreNextCategorySelect: boolean;
  isServerUpdating: boolean;
  handleCategorySelect: (catID: string, catName?: string) => Promise<void>;
  handleSubCategorySelect: (subcatID: string) => Promise<void>;
  fetchRankingsByCategory: () => Promise<void>;
  resetCategoryState: () => void;
  setRankingsBySubcategory: (rankings: Record<string, RankingType[]> | ((prev: Record<string, RankingType[]>) => Record<string, RankingType[]>)) => void;
  setForceUpdate: (update: number | ((prev: number) => number)) => void;
  setIgnoreNextCategorySelect: (ignore: boolean) => void;
  setIsServerUpdating: (updating: boolean) => void;
  setIsEditMode: (edit: boolean) => void;
}

export const useCategoryManagement = ({
  userId,
  pageUserId,
  categories,
  isDragInProgress,
  initializedFromUrl,
  updateSubCategory,
  updateCategoryID,
  updateSelectedCategoryName,
  updateSelectedCategoryIndex,
  updateSubCategories,
  updateSubcategoryOrder,
}: UseCategoryManagementProps): UseCategoryManagementReturn => {

  // フック初期化ログを削除

  // Local state
  const [categoryID, setCategoryID] = useState("");
  const [selectedCategoryName, setSelectedCategoryName] = useState("");
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(-1);
  const [subCategory, setSubCategory] = useState("");
  const [subCategories, setSubCategories] = useState<CategoryType[]>([]);
  const [subcategoryOrder, setSubcategoryOrder] = useState<string[]>([]);
  const [rankingsBySubcategory, setRankingsBySubcategory] = useState<Record<string, RankingType[]>>({});

  // デバッグ用：setRankingsBySubcategoryをラップしてログ出力
  const setRankingsBySubcategoryWithLog = useCallback((
    value: Record<string, RankingType[]> | ((prev: Record<string, RankingType[]>) => Record<string, RankingType[]>)
  ) => {
    const stack = new Error().stack;
    console.log('🔍 [FLICKER DEBUG] setRankingsBySubcategory called from:', {
      stack: stack?.split('\n').slice(1, 4).join('\n'),
      timestamp: new Date().toISOString()
    });

    if (typeof value === 'function') {
      setRankingsBySubcategory(prev => {
        const newValue = value(prev);
        console.log('🔍 [FLICKER DEBUG] setRankingsBySubcategory function update:', {
          previousKeys: Object.keys(prev),
          previousTotal: Object.values(prev).reduce((sum, arr) => sum + arr.length, 0),
          newKeys: Object.keys(newValue),
          newTotal: Object.values(newValue).reduce((sum, arr) => sum + arr.length, 0),
          newValue,
          timestamp: new Date().toISOString()
        });
        return newValue;
      });
    } else {
      console.log('🔍 [FLICKER DEBUG] setRankingsBySubcategory direct update:', {
        newKeys: Object.keys(value),
        newTotal: Object.values(value).reduce((sum, arr) => sum + arr.length, 0),
        newValue: value,
        timestamp: new Date().toISOString()
      });
      setRankingsBySubcategory(value);
    }
  }, []);
  const [forceUpdate, setForceUpdate] = useState(0);

  // デバッグ用：rankingsBySubcategoryの変更をログ出力
  useEffect(() => {
    console.log('🔍 [FLICKER DEBUG] rankingsBySubcategory changed:', {
      keys: Object.keys(rankingsBySubcategory),
      totalRankings: Object.values(rankingsBySubcategory).reduce((sum, arr) => sum + arr.length, 0),
      data: rankingsBySubcategory,
      timestamp: new Date().toISOString()
    });
  }, [rankingsBySubcategory]);
  const [ignoreNextCategorySelect, setIgnoreNextCategorySelect] = useState(false);
  const [isServerUpdating, setIsServerUpdating] = useState(false);
  const [isFetchingRankings, setIsFetchingRankings] = useState(false);

  // 重複実行防止用のref
  const fetchingCategoryRef = useRef<string | null>(null);

  // キャッシュ機能：カテゴリごとのランキングデータを保持
  const [rankingCache, setRankingCache] = useState<Record<string, Record<string, RankingType[]>>>({});

  // 最新のcategoryIDを追跡するためのref
  const currentCategoryIDRef = useRef(categoryID);

  // categoryIDが変更されたらrefも更新
  useEffect(() => {
    currentCategoryIDRef.current = categoryID;
  }, [categoryID]);

  const setIsEditMode = useCallback((edit: boolean) => {
    // This is a placeholder - the actual setIsEditMode should be passed from parent
    // setIsEditModeログを削除
  }, []);

  const handleSubCategorySelect = useCallback(
    async (subcatID: string) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔥 [SCROLL DEBUG] handleSubCategorySelect呼び出し:', {
          subcatID,
          currentSubCategory: subCategory,
          isDragInProgress
        });
      }

      // ドラッグ中の場合はサブカテゴリ選択をスキップ
      if (isDragInProgress) {
        if (process.env.NODE_ENV === 'development') {
          console.log('❌ [SCROLL DEBUG] ドラッグ中のためスキップ');
        }
        return;
      }

      // 同じサブカテゴリが選択された場合でもスクロール処理は実行
      const shouldScroll = subcatID !== subCategory;

      if (process.env.NODE_ENV === 'development') {
        console.log('📊 [SCROLL DEBUG] サブカテゴリ選択処理:', {
          shouldScroll,
          willUpdateState: shouldScroll
        });
      }

      // 状態更新（異なるサブカテゴリの場合のみ）
      if (shouldScroll) {
        requestAnimationFrame(() => {
          setSubCategory(subcatID);
          updateSubCategory(subcatID);
        });
      }

      // スクロール処理を無効化（自動スクロールによる挙動不安定を防止）
      // ユーザーの明示的なクリック時のみスクロールを実行するため、ここでは無効化

      // まだこのサブカテゴリのランキングが読み込まれていない場合は取得
      if (!rankingsBySubcategory[subcatID]) {
        try {
          // サブカテゴリIDが有効かチェック
          if (!subcatID) {
            throw new Error('無効なサブカテゴリIDが指定されました');
          }
          
          // ログインユーザーIDが空の場合はページのユーザーIDを使用
          const requestUserId = userId || pageUserId;
          
          if (!requestUserId) {
            throw new Error('ユーザーIDが取得できないため、ランキングを表示できません');
          }
          
          // APIエンドポイントのパラメータ名を修正（サーバー側の期待するパラメータ名に合わせる）
          const apiUrl = `/api/getRankBySubcategory?user_ID=${encodeURIComponent(requestUserId)}&subCategoryID=${encodeURIComponent(subcatID)}`;

          // APIリクエストを実行
          const response = await fetch(apiUrl);
          const responseText = await response.text();
          let json;
          
          try {
            // レスポンスが空でない場合のみJSONとしてパース
            json = responseText.trim() ? JSON.parse(responseText) : {};
          } catch (parseError) {
            // JSONパースエラーの処理
            throw new Error(`サブカテゴリ ${subcatID} のレスポンスが不正なJSONフォーマットです`);
          }
          
          if (!response.ok) {
            // API応答エラーの処理
            const errorMessage = json && typeof json === 'object' && json.error ? json.error : response.statusText || '不明なエラー';
            throw new Error(`サブカテゴリ ${subcatID} のランキング取得に失敗: ${errorMessage}`);
          }
          
          // APIから取得したランキングデータを正規化
          const rankings = Array.isArray(json)
            ? json
            : Array.isArray(json.ranks)
            ? json.ranks
            : [];
          
          // order値の昇順でソート（小さい値が上に表示）
          const sortedRankings = [...rankings].sort((a, b) => {
            const orderA = a.order !== undefined ? a.order : 999999;
            const orderB = b.order !== undefined ? b.order : 999999;
            return orderA - orderB; // 昇順（order値が小さいものが上）
          });
          
          // ランキング取得成功
          setRankingsBySubcategoryWithLog(prev => {
            const newData = {
              ...prev,
              [subcatID]: sortedRankings
            };

            // キャッシュも更新
            if (categoryID) {
              setRankingCache(prevCache => ({
                ...prevCache,
                [categoryID]: newData
              }));
              console.log('💾 サブカテゴリ選択: キャッシュに保存:', categoryID, subcatID);
            }

            return newData;
          });
        } catch (error) {
          // ランキング取得エラーの処理
          console.error('サブカテゴリ選択時のAPI呼び出しエラー:', error);
          setRankingsBySubcategoryWithLog(prev => {
            const newData = {
              ...prev,
              [subcatID]: []
            };

            // エラー時はキャッシュしない（無限ループを防ぐため）
            // 次回のリクエスト時に再度APIを呼び出すことで、一時的なエラーから回復可能

            return newData;
          });
        }
      }
      
      // URLを更新
      // [username]ページの場合はサブカテゴリ選択時はURL更新しない（カテゴリ選択時のみ）
      if (categoryID && !window.location.pathname.match(/^\/[^\/]+$/)) {
        // 従来のページの場合：カテゴリとサブカテゴリのパラメータをURLに保存
        // updateUrlWithCategory 関数は削除済み - 新URLページでは使用されない
      }
    },
    [categoryID, rankingsBySubcategory, subCategory, userId, pageUserId, isDragInProgress, updateSubCategory]
  );

  const handleCategorySelect = useCallback(
    async (catID: string, catName: string = "") => {
      // 重要: 必ずcatIDとcategoryIDを文字列型に変換して型の一貫性を確保
      const normalizedCatID = String(catID);
      const normalizedCurrentCatID = String(categoryID);
      
      // 初期化時のカテゴリ選択を無視するフラグをチェック
      if (initializedFromUrl && ignoreNextCategorySelect) {
        // 初期化フラグをリセットしてカテゴリ切り替えを続行
        setIgnoreNextCategorySelect(false); // フラグをリセット
      }
      
      // 選択されたカテゴリを取得
      const selectedCategory = categories.find((c) => String(c.id) === normalizedCatID);
      if (!selectedCategory) {
        // 選択されたカテゴリが見つからない場合の処理
        return;
      }
      
      // カテゴリ名を確実に取得
      const categoryName = catName || selectedCategory.category_name;
      
      // カテゴリ状態を更新
      setIsEditMode(false);
      setCategoryID(normalizedCatID);
      updateCategoryID(normalizedCatID);
      setSelectedCategoryName(categoryName);
      updateSelectedCategoryName(categoryName);
      
      // 選択されたカテゴリのインデックスを更新
      const categoryIndex = categories.findIndex((c) => String(c.id) === normalizedCatID);
      setSelectedCategoryIndex(categoryIndex);
      updateSelectedCategoryIndex(categoryIndex);
      
      // 同じカテゴリを選択した場合は、サブカテゴリを維持するが、カテゴリ名は更新する
      // 文字列型に統一した比較を行う
      if (normalizedCatID === normalizedCurrentCatID) {
        // 強制的に再レンダリングをトリガーするためのフラグを更新
        setForceUpdate(prev => prev + 1);
        return; // 同じカテゴリの場合はここで終了
      }
      
      // サブカテゴリを更新
      const subcats = (selectedCategory.subcategories || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
      setSubCategories(subcats);
      updateSubCategories(subcats);
      const subcatOrder = subcats.map((s) => s.id);
      setSubcategoryOrder(subcatOrder);
      updateSubcategoryOrder(subcatOrder);

      // 最初のサブカテゴリを選択するか、サブカテゴリがない場合は空に設定
      let selectedSubcategoryId = "";
      if (subcats.length > 0) {
        selectedSubcategoryId = subcats[0].id;
      }
      setSubCategory(selectedSubcategoryId);
      updateSubCategory(selectedSubcategoryId);

      // キャッシュからランキングデータを復元（存在する場合）
      const cachedData = rankingCache[normalizedCatID];
      console.log('🔍 [FLICKER DEBUG] handleCategorySelect - cache check:', {
        categoryID: normalizedCatID,
        hasCachedData: !!cachedData,
        cachedKeys: cachedData ? Object.keys(cachedData) : [],
        currentRankingsKeys: Object.keys(rankingsBySubcategory),
        timestamp: new Date().toISOString()
      });

      if (cachedData) {
        // キャッシュデータがある場合は即座に設定（ちらつき防止）
        console.log('🔍 [FLICKER DEBUG] Setting cached data:', cachedData);
        setRankingsBySubcategoryWithLog(cachedData);
      }
      // キャッシュがない場合は既存データを保持したまま新しいデータを取得
      // 空のオブジェクトで初期化しない（ちらつき防止）

      // URLを更新（ページリロードなし）
      // [username]ページの場合はカスタムハンドラーでURL更新を行うため、ここではスキップ
      if (!window.location.pathname.match(/^\/[^\/]+$/)) {
        // 従来のページの場合：クエリパラメータでURL更新
        // updateUrlWithCategory 関数は削除済み - 新URLページでは使用されない
      }
      
      // ログイン中のユーザーIDがない場合はページのユーザーIDを使用
      const requestUserId = userId || pageUserId;
      
      // カテゴリ切り替え時にランキングデータを取得する非同期関数
      const fetchRankingsForCategory = async () => {
        // ドラッグ中は実行しない
        if (isDragInProgress) {
          console.log('ドラッグ中のため、fetchRankingsForCategory をスキップ');
          return;
        }

        // キャッシュが存在し、かつ有効なデータがある場合は、バックグラウンド更新をスキップ
        const cachedData = rankingCache[normalizedCatID];
        const hasValidCache = cachedData && Object.keys(cachedData).length > 0 &&
          Object.values(cachedData).some(rankings => Array.isArray(rankings) && rankings.length > 0);

        if (hasValidCache) {
          // キャッシュがある場合は即座に設定してバックグラウンド更新をスキップ
          setRankingsBySubcategoryWithLog(cachedData);
          return;
        }

        try {
          if (!requestUserId) {
            return;
          }
          
          // まず、すべてのランキングデータを取得
          const allRankingsUrl = `/api/getRanks?user_ID=${encodeURIComponent(requestUserId)}`;

          const allRankingsResponse = await fetch(allRankingsUrl);
          const allRankingsText = await allRankingsResponse.text();
          
          let allRankingsData;
          try {
            allRankingsData = allRankingsText.trim() ? JSON.parse(allRankingsText) : [];
          } catch (parseError) {
            allRankingsData = [];
          }
          
          // レスポンス形式に応じてデータを取得
          const allRankings = Array.isArray(allRankingsData) 
            ? allRankingsData 
            : allRankingsData.ranks && Array.isArray(allRankingsData.ranks) 
              ? allRankingsData.ranks 
              : [];
          
          // サブカテゴリごとにランキングを振り分ける
          const newRankingsBySubcategory: Record<string, RankingType[]> = {};
          
          if (subcats && subcats.length > 0) {
            // 各サブカテゴリに対して処理を実行
            const fetchPromises = subcats.map(async (subcat) => {
              // まず、全体のランキングからこのサブカテゴリに属するランキングをフィルタリング
              let rankingsForSubcat = allRankings.filter(ranking => 
                ranking.subCategory_ID === subcat.id || ranking.subcategory_id === subcat.id
              );
              
              // ランキングが見つからない場合は個別に取得
              if (rankingsForSubcat.length === 0) {
                try {
                  const specificUrl = `/api/getRankBySubcategory?user_ID=${encodeURIComponent(requestUserId)}&subCategoryID=${encodeURIComponent(subcat.id)}`;
                  
                  const specificResponse = await fetch(specificUrl);
                  const specificText = await specificResponse.text();
                  
                  let specificData;
                  try {
                    specificData = specificText.trim() ? JSON.parse(specificText) : {};
                  } catch (parseError) {
                    specificData = {};
                  }
                  
                  const specificRankings = Array.isArray(specificData) ? specificData : Array.isArray(specificData.ranks) ? specificData.ranks : [];
                  
                  if (specificRankings.length > 0) {
                    rankingsForSubcat = specificRankings;
                  }
                } catch (error) {
                  // 個別取得エラーの処理
                }
              }
              
              return { subcatId: subcat.id, rankings: rankingsForSubcat, name: subcat.category_name };
            });
            
            // すべてのサブカテゴリの取得が完了するのを待つ
            const results = await Promise.all(fetchPromises);
            
            // 結果をマップに格納
            results.forEach(({ subcatId, rankings }) => {
              newRankingsBySubcategory[subcatId] = rankings;
            });
          }
          
          // キャッシュが存在する場合は、新しいデータが取得できた場合のみ更新
          const hasCache = rankingCache[normalizedCatID];
          const hasNewData = Object.keys(newRankingsBySubcategory).some(key =>
            newRankingsBySubcategory[key].length > 0
          );

          if (!hasCache || hasNewData) {
            // キャッシュに保存
            setRankingCache(prev => ({
              ...prev,
              [normalizedCatID]: newRankingsBySubcategory
            }));

            // 現在選択中のカテゴリの場合のみUIを更新（refから最新の値を取得）
            const currentCategoryID = currentCategoryIDRef.current;
            if (normalizedCatID === currentCategoryID) {
              // 既存のデータを保持しながら新しいデータとマージしてちらつきを防ぐ
              setRankingsBySubcategoryWithLog(prev => ({
                ...prev,
                ...newRankingsBySubcategory
              }));
            }
          }
          
        } catch (error) {
          // カテゴリ切り替え時のランキング取得エラー処理
        }
      };
      
      // 非同期関数を呼び出し
      fetchRankingsForCategory();
      
      // 強制的に再レンダリングをトリガーするためのフラグを更新
      setForceUpdate(prev => prev + 1);
      
      // カテゴリ情報をローカルストレージに保存
      try {
        localStorage.setItem(`categoryName_${normalizedCatID}`, categoryName);
        localStorage.setItem('currentCategoryID', normalizedCatID);
      } catch (error) {
        // ローカルストレージ保存エラーの処理
      }
    },
    [categories, categoryID, ignoreNextCategorySelect, initializedFromUrl, userId, pageUserId, isDragInProgress, updateCategoryID, updateSelectedCategoryName, updateSelectedCategoryIndex, updateSubCategories, updateSubcategoryOrder, updateSubCategory]
  );

  const fetchRankingsByCategory = useCallback(async (subcatsToFetch?: SubCategoryType[], targetCategoryID?: string) => {
    // ドラッグ中、サーバー更新中、または既に取得中の場合は実行しない
    if (isDragInProgress || isServerUpdating || isFetchingRankings) {
      return;
    }

    // 対象カテゴリIDを決定（引数で指定されていない場合は現在のカテゴリID）
    const targetCatID = targetCategoryID || categoryID;

    // 同じカテゴリで既に取得中の場合はスキップ（重複実行防止）
    if (fetchingCategoryRef.current === targetCatID) {
      console.log('🔍 [FLICKER DEBUG] fetchRankingsByCategory already fetching for category:', targetCatID);
      return;
    }

    // キャッシュが存在し、かつ有効なデータがある場合は実行しない
    const cachedData = targetCatID ? rankingCache[targetCatID] : null;
    const hasValidCache = cachedData && Object.keys(cachedData).length > 0 &&
      Object.values(cachedData).some(rankings => Array.isArray(rankings) && rankings.length > 0);

    if (hasValidCache) {
      return;
    }

    // 取得対象のサブカテゴリを決定
    const targetSubcats = subcatsToFetch || subCategories;

    if (targetSubcats.length === 0) {
      return;
    }

    setIsFetchingRankings(true);
    fetchingCategoryRef.current = targetCatID; // 取得中のカテゴリを記録

    // AbortControllerを作成してリクエストをキャンセル可能にする
    const abortController = new AbortController();

    try {
      const obj: Record<string, RankingType[]> = {};
      await Promise.all(
        targetSubcats.map(async (subcat) => {
          try {
            const timeoutId = setTimeout(() => abortController.abort(), 5000); // 5秒でタイムアウト

            const res = await fetch(
              `/api/getRankBySubcategory?user_ID=${encodeURIComponent(pageUserId)}&subCategoryID=${encodeURIComponent(subcat.id)}`,
              {
                signal: abortController.signal,
                headers: {
                  'Content-Type': 'application/json',
                }
              }
            );

            clearTimeout(timeoutId);

            if (res.ok) {
              const text = await res.text();
              if (text.trim()) {
                try {
                  const json = JSON.parse(text);
                  const items = Array.isArray(json)
                    ? json
                    : Array.isArray(json.ranks)
                    ? json.ranks
                    : [];

                  obj[subcat.id] = items;
                } catch (parseError) {
                  obj[subcat.id] = [];
                }
              } else {
                obj[subcat.id] = [];
              }
            } else {
              // エラーの場合は空配列を設定（ログを出力しない）
              obj[subcat.id] = [];
            }
          } catch (error) {
            // 個別のサブカテゴリでエラーが発生した場合は空配列を設定（ログを出力しない）
            obj[subcat.id] = [];
          }
        })
      );

      // キャッシュが存在する場合は、新しいデータが取得できた場合のみ更新
      const hasCache = targetCatID && rankingCache[targetCatID];
      const hasNewData = Object.keys(obj).some(key => obj[key].length > 0);

      if (!hasCache || hasNewData) {
        // キャッシュに保存
        if (targetCatID) {
          setRankingCache(prev => ({
            ...prev,
            [targetCatID]: obj
          }));
        }

        // 現在選択中のカテゴリの場合のみUIを更新（refから最新の値を取得）
        const currentCategoryID = currentCategoryIDRef.current;
        console.log('🔍 [FLICKER DEBUG] fetchRankingsByCategory update check:', {
          targetCatID,
          currentCategoryID,
          willUpdate: targetCatID === currentCategoryID,
          newDataKeys: Object.keys(obj),
          timestamp: new Date().toISOString()
        });

        if (targetCatID === currentCategoryID) {
          // 既存のデータを保持しながら新しいデータとマージしてちらつきを防ぐ
          console.log('🔍 [FLICKER DEBUG] fetchRankingsByCategory updating rankings:', {
            previousKeys: Object.keys(rankingsBySubcategory),
            newKeys: Object.keys(obj),
            mergedData: { ...rankingsBySubcategory, ...obj }
          });
          setRankingsBySubcategoryWithLog(prev => ({
            ...prev,
            ...obj
          }));
        }
      }
    } catch (err) {
      // 全体的なエラーの場合は何もしない
    } finally {
      setIsFetchingRankings(false);
      fetchingCategoryRef.current = null; // 取得完了時にリセット
    }
  }, [pageUserId, isDragInProgress, isServerUpdating, isFetchingRankings, categoryID, rankingCache]);

  // サブカテゴリが変更された時にランキングデータを自動取得
  const subCategoriesKey = useMemo(() =>
    subCategories.map(s => s.id).sort().join(','),
    [subCategories]
  );

  useEffect(() => {
    let isCancelled = false;

    console.log('🔍 [FLICKER DEBUG] useEffect triggered:', {
      subCategoriesLength: subCategories.length,
      categoryID,
      isDragInProgress,
      currentRankingsKeys: Object.keys(rankingsBySubcategory),
      timestamp: new Date().toISOString()
    });

    if (subCategories.length > 0 && !isCancelled && !isDragInProgress) {
      // ドラッグ中は自動取得をスキップ（パフォーマンス最適化）

      // キャッシュが存在し、かつ有効なデータがある場合は自動取得をスキップ
      const cachedData = categoryID ? rankingCache[categoryID] : null;
      const hasValidCache = cachedData && Object.keys(cachedData).length > 0;

      // 現在のrankingsBySubcategoryにデータがある場合は、キャッシュがなくても再取得をスキップ
      const hasCurrentData = Object.keys(rankingsBySubcategory).length > 0 &&
        Object.values(rankingsBySubcategory).some(rankings => Array.isArray(rankings) && rankings.length > 0);

      console.log('🔍 [FLICKER DEBUG] useEffect cache check:', {
        categoryID,
        hasCachedData: !!cachedData,
        hasValidCache,
        hasCurrentData,
        cachedKeys: cachedData ? Object.keys(cachedData) : [],
        currentKeys: Object.keys(rankingsBySubcategory),
        timestamp: new Date().toISOString()
      });

      if (hasValidCache) {
        // キャッシュからデータを設定（即座に反映してちらつきを防ぐ）
        console.log('🔍 [FLICKER DEBUG] useEffect setting cached data:', cachedData);
        setRankingsBySubcategoryWithLog(cachedData);
        return;
      }

      if (hasCurrentData) {
        // 現在のデータがある場合は再取得をスキップ（無限ループ防止）
        console.log('🔍 [FLICKER DEBUG] useEffect has current data, skipping fetch');
        return;
      }

      // キャッシュも現在データもない場合のみ新しいデータを取得
      console.log('🔍 [FLICKER DEBUG] useEffect no cache and no current data, fetching new');

      // デバウンス処理でAPI呼び出しを制限（パフォーマンス最適化）
      const timeoutId = setTimeout(() => {
        if (!isCancelled) {
          fetchRankingsByCategory(subCategories);
        }
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        isCancelled = true;
      };
    }

    return () => {
      isCancelled = true;
    };
  }, [subCategoriesKey, categoryID, isDragInProgress]); // rankingCacheを依存関係から除外して無限ループを防ぐ

  const resetCategoryState = useCallback(() => {
    setSubCategory("");
    updateSubCategory("");
    setCategoryID("");
    updateCategoryID("");
    setSelectedCategoryName("");
    updateSelectedCategoryName("");
  }, [updateSubCategory, updateCategoryID, updateSelectedCategoryName]);

  return {
    categoryID,
    selectedCategoryName,
    selectedCategoryIndex,
    subCategory,
    subCategories,
    subcategoryOrder,
    rankingsBySubcategory,
    forceUpdate,
    ignoreNextCategorySelect,
    isServerUpdating,
    handleCategorySelect,
    handleSubCategorySelect,
    fetchRankingsByCategory,
    resetCategoryState,
    setRankingsBySubcategory: setRankingsBySubcategoryWithLog,
    setForceUpdate,
    setIgnoreNextCategorySelect,
    setIsServerUpdating,
    setIsEditMode,
  };
};